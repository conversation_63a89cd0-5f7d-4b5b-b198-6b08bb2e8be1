<script setup lang="ts">
import { Button } from "@/components/ui/button";

interface Props {
    targetId?: string;
    text?: string;
}

const props = withDefaults(defineProps<Props>(), {
    targetId: "main",
    text: "Skip to content",
});

const skipToContent = () => {
    const target = document.getElementById(props.targetId);
    if (target) {
        target.focus({ preventScroll: false });
        target.scrollIntoView({ behavior: "smooth", block: "start" });
    }
};
</script>

<template>
    <Button
        @click="skipToContent"
        variant="outline"
        size="sm"
        class="sr-only focus:not-sr-only focus:absolute focus:top-2 focus:left-2 focus:z-50 bg-white border-slate-300 text-slate-800 shadow-lg hover:bg-slate-50 focus:ring-2 focus:ring-sky-600 focus:ring-offset-2"
        :aria-label="`${text} - Press Enter to skip navigation`"
    >
        {{ text }}
    </Button>
</template>
